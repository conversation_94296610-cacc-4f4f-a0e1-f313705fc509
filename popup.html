<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>AI简历助手设置</title>
  <link rel="stylesheet" href="styles.css">
  <script src="config.js"></script>
  <style>
    body {
      width: 320px;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f7fa;
      margin: 0;
    }

    .error-container {
      display: none;
      background: #fff2f0;
      border: 1px solid #ffccc7;
      padding: 12px;
      margin-bottom: 16px;
      border-radius: 4px;
      color: #ff4d4f;
      font-size: 14px;
      line-height: 1.5;
      position: relative;
    }

    .error-container.show {
      display: block;
    }

    .error-container .close-btn {
      position: absolute;
      right: 8px;
      top: 8px;
      cursor: pointer;
      color: #ff7875;
      font-size: 12px;
    }

    .settings-container {
      display: flex;
      flex-direction: column;
      gap: 20px;
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }

    .setting-item {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .setting-header {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    label {
      font-weight: 500;
      color: #333;
      font-size: 14px;
    }

    .tooltip {
      color: #8c8c8c;
      font-size: 12px;
      line-height: 1.5;
      margin-top: 4px;
      padding: 8px 12px;
      background: #f5f7fa;
      border-radius: 4px;
      border-left: 3px solid #1677ff;
    }

    .tooltip a {
      color: #1677ff;
      text-decoration: none;
    }

    .tooltip a:hover {
      text-decoration: underline;
    }

    input[type="text"],
    input[type="number"],
    textarea {
      padding: 8px 12px;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      font-size: 14px;
      transition: all 0.3s;
    }

    input[type="text"]:focus,
    input[type="number"]:focus,
    textarea:focus {
      outline: none;
      border-color: #1677ff;
      box-shadow: 0 0 0 2px rgba(22,119,255,0.1);
    }

    input[type="number"] {
      width: 100px;
    }

    select.model-select {
      padding: 8px 12px;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      font-size: 14px;
      width: 200px;
      background-color: white;
      transition: all 0.3s;
      cursor: pointer;
      position: relative;
      z-index: 10;
      pointer-events: auto;
    }

    select.model-select:focus {
      outline: none;
      border-color: #1677ff;
      box-shadow: 0 0 0 2px rgba(22,119,255,0.1);
    }

    textarea {
      height: 100px;
      resize: vertical;
      font-family: inherit;
    }

    .save-btn {
      background-color: #1677ff;
      color: white;
      border: none;
      padding: 10px 16px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s;
      margin-top: 10px;
    }

    .save-btn:hover {
      background-color: #4096ff;
      transform: translateY(-1px);
    }

    .save-btn:active {
      transform: translateY(0);
    }

    .status {
      margin-top: 10px;
      padding: 8px;
      border-radius: 4px;
      display: none;
    }

    .status.success {
      display: block;
      background-color: #f6ffed;
      border: 1px solid #b7eb8f;
      color: #222edc;
    }

    .status.error {
      display: block;
      background-color: #fff2f0;
      border: 1px solid #ffccc7;
      color: #ff4d4f;
    }

    .switch-label {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .switch-wrapper {
      position: relative;
      width: 60px;
    }

    .switch {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 30px;
      background-color: #ccc;
      cursor: pointer;
    }

    .switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .slider {
      position: absolute;
      top: 2px;
      left: 2px;
      width: 26px;
      height: 26px;
      border-radius: 50%;
      background-color: #fff;
      transition: transform 0.3s;
    }

    .switch input:checked + .slider {
      transform: translateX(26px);
    }

    .env-label {
      font-weight: 500;
      color: #333;
    }

    .checkbox-label {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: normal;
      cursor: pointer;
    }

    .checkbox-label input[type="checkbox"] {
      width: 16px;
      height: 16px;
      cursor: pointer;
    }

    .credits-details {
      background: #fafafa;
      padding: 12px;
      border-radius: 6px;
      margin-top: 8px;
    }

    .credit-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      font-size: 13px;
      color: #333;
    }

    .credit-item:last-child {
      margin-bottom: 0;
    }

    .warning-text {
      color: #ff4d4f;
      border-left-color: #ff4d4f;
    }

    #remainingCredits {
      font-weight: 500;
      color: #1677ff;
    }

    #remainingCredits.warning {
      color: #ff4d4f;
    }

    .start-btn {
      display: block;
      width: 100%;
      padding: 10px;
      margin-top: 10px;
      background-color: #06b95d;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.3s;
    }

    .start-btn:hover {
      background-color: #40a9ff;
    }

    .env-button-group {
      display: flex;
      gap: 10px;
    }

    .env-button {
      background-color: #f5f7fa;
      border: 1px solid #d9d9d9;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.3s;
    }

    .env-button.active {
      background-color: #1677ff;
      color: white;
    }
  </style>
</head>
<body>
  <div id="errorContainer" class="error-container">
    <span id="errorMessage"></span>
    <span class="close-btn" onclick="this.parentElement.classList.remove('show')">✕</span>
  </div>

  <div class="settings-container">
    <div class="setting-item">
      <div class="setting-header">
        <label for="apiKey">API密钥</label>
      </div>
      <input type="text" id="apiKey" placeholder="请输入您的API密钥" value="trial-token-123">
      <div class="tooltip">
        试用密钥：trial-token-123，获取专享API密钥请访问：<a href='https://hrassistent.online' target='_blank'>https://hrassistent.online</a> 注册账号获取
      </div>
    </div>

    <div class="setting-item credits-info" style="display: none;">
      <div class="setting-header">
        <label>积分信息(每次评估消耗1积分)</label>
      </div>
      <div class="credits-details">
        <div class="credit-item">
          <span>总积分：</span>
          <span id="totalCredits">-</span>
        </div>
        <div class="credit-item">
          <span>已用积分：</span>
          <span id="usedCredits">-</span>
        </div>
        <div class="credit-item">
          <span>剩余积分：</span>
          <span id="remainingCredits">-</span>
        </div>
        <div class="credit-item">
          <span>有效期至：</span>
          <span id="expiresAt">-</span>
        </div>
      </div>
      <div class="tooltip warning-text" id="creditsWarning" style="display: none;">
        积分即将用完，请及时联系微信：<a href="#">autoairesume</a>进行充值
      </div>
    </div>

    <div class="setting-item">
      <div class="setting-header">
        <label for="scoreThreshold">评分阈值</label>
      </div>
      <input type="number" id="scoreThreshold" min="0" max="100" value="80">
      <div class="tooltip">
        当简历评分高于阈值时，系统将自动收藏该简历
      </div>
    </div>

    <div class="setting-item">
      <div class="setting-header">
        <label>阈值触发动作</label>
      </div>
      <div style="display: flex; flex-direction: column; gap: 10px; margin-top: 8px;">
        <label class="checkbox-label">
          <input type="checkbox" id="autoFavorite" checked>
          <span>自动收藏</span>
        </label>
        <label class="checkbox-label">
          <input type="checkbox" id="autoGreet">
          <span>自动打招呼</span>
        </label>
        <label class="checkbox-label">
          <input type="checkbox" id="autoShare" checked>
          <span>自动生成分享链接</span>
        </label>
      </div>
      <div class="tooltip">
        设置当简历评分高于阈值时，系统将执行的自动操作
      </div>
    </div>

    <div class="setting-item">
      <div class="setting-header">
        <label for="prompt-templates">评估提示词模板</label>
        <button id="add-template" class="small-button">添加模板</button>
      </div>
      <div id="template-list">
        <!-- 模板列表将通过JavaScript动态生成 -->
      </div>
      <div class="template-editor" style="display: none;">
        <input type="text" id="template-name" placeholder="模板名称" class="template-name-input">
        <textarea id="template-content" placeholder="请输入评估规则，包含基础分和加分项" class="template-content-textarea"></textarea>
        <div class="template-actions">
          <button id="save-template" class="small-button">保存模板</button>
          <button id="cancel-template" class="small-button">取消</button>
        </div>
      </div>
      <div class="tooltip">
        提示：评分规则建议包含以下内容：<br>
        1. 基础分（0分）<br>
        2. 加分项（40分）：学历、稳定性、行业经验等<br>
        3. 每项评分标准要明确具体<br>
      </div>
    </div>

    <div class="setting-item">
      <div class="setting-header">
        <label for="model">AI模型</label>
      </div>
      <select id="model" class="model-select">
        <!-- 选项将由JavaScript动态填充 -->
      </select>
      <div class="tooltip">
        选择用于评估简历的AI模型
      </div>
    </div>

    <div class="setting-item" id="customEndpointSection" style="display: none;">
      <div class="setting-header">
        <label for="useCustomEndpoint">自定义端点设置</label>
      </div>
      <label class="checkbox-label">
        <input type="checkbox" id="useCustomEndpoint">
        <span>使用自定义端点</span>
      </label>
      <div class="tooltip">
        选择是否使用API返回的自定义端点进行简历评估
      </div>
    </div>

    <div class="setting-item env-switch">
      <label class="env-label">环境切换</label>
      <div class="env-button-group">
        <button class="env-button" data-env="production">生产环境</button>
        <button class="env-button" data-env="development">开发环境</button>
      </div>
      <div class="tooltip">仅在开发模式下显示环境切换选项</div>
    </div>
    <div id="status" class="status"></div>
    <button class="save-btn" id="saveSettings">保存设置</button>
    <button class="start-btn" id="startNow">立即开始</button>
  </div>
  <script src="popup.js"></script>
</body>
</html> 